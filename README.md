# 🚀 Kripto Trading Bot - n8n Tabanlı AI Sistemi

> **Telegram AI chatbot ile yönetilen, ger<PERSON><PERSON> zamanlı piyasa analizi yapan ve Binance TR entegrasyonlu kripto trading sistemi**

## 📊 Sistem Özellikleri

### 🎯 **Temel Özellikler**
- **Real-time Market Analysis**: CoinGecko, Binance API ile sürekli piyasa takibi
- **AI-Powered Signals**: OpenAI GPT-4 ile akıllı trading sinyalleri
- **Telegram Control**: Bot üzerinden tam kontrol ve monitoring
- **Binance TR Integration**: Güvenli ve otomatik trading işlemleri
- **Risk Management**: Kapsamlı risk kontrolü ve güvenlik önlemleri

### 🔧 **Teknik Altyapı**
- **n8n Workflow Automation**: 525+ node ile esnek otomasyon
- **Docker Containerization**: Kolay kurulum ve ölçeklenebilirlik
- **PostgreSQL Database**: Güvenilir veri saklama
- **Redis Caching**: Hızlı veri erişimi
- **SSL/HTTPS Support**: Production-ready güvenlik

## 🏗️ Sistem Mimarisi

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Sources  │    │   AI Analysis   │    │  Trading Exec   │
│                 │    │                 │    │                 │
│ • CoinGecko     │───▶│ • OpenAI GPT-4  │───▶│ • Binance TR    │
│ • Binance API   │    │ • Technical     │    │ • Risk Mgmt     │
│ • Fear & Greed  │    │   Indicators    │    │ • Stop-Loss     │
│ • News APIs     │    │ • Sentiment     │    │ • Take-Profit   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Telegram Bot    │
                    │                 │
                    │ • Commands      │
                    │ • Notifications │
                    │ • Reports       │
                    │ • Controls      │
                    └─────────────────┘
```

## 📁 Proje Yapısı

```
crypto-trading-bot/
├── 📄 README.md                          # Bu dosya
├── 📄 crypto-trading-system-plan.md      # Detaylı sistem planı
├── 📄 deployment-guide.md                # Kurulum rehberi
├── 📄 binance-integration-guide.md       # Binance API rehberi
├── 📄 risk-management-security.md        # Risk ve güvenlik
├── 🔧 docker-compose.yml                 # Docker konfigürasyonu
├── 🔧 .env.example                       # Environment variables
├── 📊 workflows/
│   ├── workflow-1-market-data-collector.json
│   ├── workflow-2-telegram-bot.json
│   └── workflow-3-ai-analyzer.json
├── 📜 scripts/
│   ├── backup.sh                         # Backup scripti
│   └── deploy.sh                         # Deployment scripti
└── 📚 docs/
    ├── api-documentation.md
    ├── troubleshooting.md
    └── performance-tuning.md
```

## 🚀 Hızlı Başlangıç

### 1. Ön Gereksinimler
```bash
# Docker ve Docker Compose kurulu olmalı
docker --version
docker-compose --version

# Git ile projeyi klonlayın
git clone https://github.com/inkbytefo/crypto-trading-bot.git
cd crypto-trading-bot
```

### 2. Environment Kurulumu
```bash
# .env dosyasını oluşturun
cp .env.example .env

# API anahtarlarınızı ekleyin
nano .env
```

### 3. Sistemi Başlatın
```bash
# Docker container'ları başlat
docker-compose up -d

# n8n arayüzüne erişin
open http://localhost:5678
```

### 4. Workflow'ları İçe Aktarın
1. n8n arayüzüne giriş yapın (admin/password)
2. `workflows/` klasöründeki JSON dosyalarını içe aktarın
3. Environment variables'ları kontrol edin
4. Workflow'ları aktive edin

## 📱 Telegram Bot Komutları

### 📊 **Bilgi Komutları**
```
/status     - Portfolio durumu
/balance    - Cüzdan bakiyesi  
/signals    - Trading sinyalleri
/analyze    - Coin analizi
/news       - Son haberler
/fear       - Fear & Greed Index
```

### 💰 **Trading Komutları**
```
/buy BTC 100     - 100 TRY Bitcoin al
/sell ETH 50%    - Ethereum'un %50'sini sat
/stop            - Otomatik trading durdur
/resume          - Otomatik trading başlat
```

### ⚙️ **Yönetim Komutları**
```
/settings   - Bot ayarları
/risk       - Risk parametreleri
/alerts     - Bildirim ayarları
/help       - Yardım menüsü
```

## 🛡️ Güvenlik Özellikleri

### 🔐 **API Güvenliği**
- ✅ API anahtarları environment variables'da
- ✅ IP whitelist ile erişim kontrolü
- ✅ Withdrawal permissions kapalı
- ✅ Rate limiting ve error handling

### 📊 **Risk Yönetimi**
- ✅ Maksimum pozisyon: %5 portfolio
- ✅ Günlük zarar limiti: %10
- ✅ Otomatik stop-loss: %3
- ✅ Otomatik take-profit: %8
- ✅ Position sizing algoritması

### 🚨 **Emergency Controls**
- ✅ Circuit breaker sistemi
- ✅ Telegram emergency stop
- ✅ Otomatik risk kontrolü
- ✅ Real-time monitoring

## 📈 Performans Metrikleri

### 🎯 **Takip Edilen KPI'lar**
- **Total Return**: Portfolio getirisi (%)
- **Sharpe Ratio**: Risk-adjusted return
- **Max Drawdown**: Maksimum kayıp (%)
- **Win Rate**: Kazanan işlem oranı (%)
- **Profit Factor**: Kar/Zarar oranı

### 📊 **Raporlama**
- **Günlük**: Telegram üzerinden otomatik rapor
- **Haftalık**: Detaylı performans analizi
- **Aylık**: Portfolio review ve optimizasyon

## 🔧 Konfigürasyon

### Environment Variables
```bash
# Binance TR API
BINANCE_API_KEY=your_api_key
BINANCE_SECRET_KEY=your_secret_key

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# OpenAI
OPENAI_API_KEY=your_openai_key

# Risk Parameters
MAX_POSITION_SIZE_PERCENT=5
DAILY_LOSS_LIMIT_PERCENT=10
STOP_LOSS_PERCENT=3
TAKE_PROFIT_PERCENT=8
```

### Risk Parametreleri
```javascript
const riskConfig = {
  maxPositionSize: 5,      // %5 max position
  dailyLossLimit: 10,      // %10 günlük limit
  stopLoss: 3,             // %3 stop-loss
  takeProfit: 8,           // %8 take-profit
  riskPerTrade: 2,         // %2 risk per trade
  minConfidence: 75        // %75 min sinyal güveni
};
```

## 📚 Dokümantasyon

### 📖 **Detaylı Rehberler**
- [📄 Deployment Guide](deployment-guide.md) - Kurulum ve deployment
- [🔐 Binance Integration](binance-integration-guide.md) - API entegrasyonu
- [🛡️ Risk Management](risk-management-security.md) - Güvenlik protokolleri
- [📊 System Plan](crypto-trading-system-plan.md) - Sistem mimarisi

### 🔧 **Teknik Dokümantasyon**
- [API Documentation](docs/api-documentation.md)
- [Troubleshooting](docs/troubleshooting.md)
- [Performance Tuning](docs/performance-tuning.md)

## 🚨 Önemli Uyarılar

### ⚠️ **Risk Bildirimi**
- Bu sistem **deneysel** amaçlıdır
- **Küçük tutarlarla** başlayın
- **Yatırım tavsiyesi değildir**
- **Kendi sorumluluğunuzda** kullanın

### 🔒 **Güvenlik Uyarıları**
- API anahtarlarınızı **asla paylaşmayın**
- **IP whitelist** kullanın
- **Withdrawal permissions** kapalı tutun
- **Düzenli backup** alın

## 🤝 Destek ve Katkı

### 📞 **İletişim**
- **Geliştirici**: @inkbytefo
- **GitHub**: [inkbytefo/crypto-trading-bot](https://github.com/inkbytefo/crypto-trading-bot)
- **Telegram**: @inkbytefo

### 🛠️ **Katkıda Bulunma**
1. Fork yapın
2. Feature branch oluşturun
3. Değişikliklerinizi commit edin
4. Pull request gönderin

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](LICENSE) dosyasına bakın.

---

**⚡ Hızlı, Güvenli, Karlı Trading için n8n AI Bot!**

*Made with ❤️ by inkbytefo*
