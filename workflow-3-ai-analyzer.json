{
  "name": "AI Market Analyzer",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "minutes",
              "value": 5
            }
          ]
        }
      },
      "id": "ai-analysis-trigger",
      "name": "AI Analysis Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.2,
      "position": [240, 300]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Fetch latest market data from database\n// In real implementation, this would query your database\n\nconst mockMarketData = {\n  timestamp: new Date().toISOString(),\n  prices: {\n    BTC: { usd: 48250, try: 1346789, change24h: 2.5 },\n    ETH: { usd: 2950, try: 82456, change24h: -1.2 },\n    BNB: { usd: 560, try: 15678, change24h: 0.8 }\n  },\n  technicalIndicators: {\n    BTC: {\n      RSI: 45.2,\n      MACD: 125.5,\n      SMA_20: 47800,\n      EMA_12: 48100,\n      EMA_26: 47950,\n      volume24h: 28500000000\n    }\n  },\n  fearGreedIndex: {\n    value: 35,\n    classification: 'Fear'\n  },\n  marketCap: {\n    total: 1850000000000,\n    btcDominance: 42.5\n  },\n  news: [\n    {\n      title: 'Bitcoin ETF approval rumors boost market sentiment',\n      sentiment: 'positive',\n      impact: 'high'\n    },\n    {\n      title: 'Federal Reserve hints at interest rate cuts',\n      sentiment: 'positive',\n      impact: 'medium'\n    }\n  ]\n};\n\nreturn [{ json: mockMarketData }];"
      },
      "id": "fetch-market-data",
      "name": "Fetch Market Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "resource": "text",
        "operation": "message",
        "modelId": "gpt-4",
        "prompt": "Sen bir kripto para uzmanısın. Aşağıdaki piyasa verilerini analiz et ve trading sinyali üret:\n\n{{ JSON.stringify($json, null, 2) }}\n\nAnaliz et ve şu formatta yanıt ver:\n\n**MARKET ANALYSIS**\n- Overall Sentiment: [BULLISH/BEARISH/NEUTRAL]\n- Confidence Level: [0-100]%\n- Key Factors: [En önemli 3 faktör]\n\n**TRADING SIGNALS**\n- BTC Signal: [BUY/SELL/HOLD] (Confidence: X%)\n- ETH Signal: [BUY/SELL/HOLD] (Confidence: X%)\n- BNB Signal: [BUY/SELL/HOLD] (Confidence: X%)\n\n**RISK ASSESSMENT**\n- Market Risk: [LOW/MEDIUM/HIGH]\n- Volatility: [LOW/MEDIUM/HIGH]\n- Recommended Position Size: [1-5]% of portfolio\n\n**REASONING**\n[Detaylı analiz ve gerekçeler]\n\nTürkçe yanıt ver ve yatırım tavsiyesi değil, sadece teknik analiz olduğunu belirt.",
        "options": {
          "temperature": 0.3,
          "maxTokens": 1000
        }
      },
      "id": "openai-analysis",
      "name": "OpenAI Analysis",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "typeVersion": 1.8,
      "position": [680, 300]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Parse AI analysis and generate structured signals\nconst aiResponse = $input.all()[0].json.choices[0].message.content;\nconst marketData = $input.all()[0].json;\n\n// Extract signals from AI response using regex patterns\nfunction extractSignals(text) {\n  const signals = [];\n  \n  // Extract BTC signal\n  const btcMatch = text.match(/BTC Signal:\\s*(BUY|SELL|HOLD).*?Confidence:\\s*(\\d+)%/i);\n  if (btcMatch) {\n    signals.push({\n      symbol: 'BTC',\n      action: btcMatch[1].toUpperCase(),\n      confidence: parseInt(btcMatch[2]),\n      source: 'AI_ANALYSIS',\n      timestamp: new Date().toISOString()\n    });\n  }\n  \n  // Extract ETH signal\n  const ethMatch = text.match(/ETH Signal:\\s*(BUY|SELL|HOLD).*?Confidence:\\s*(\\d+)%/i);\n  if (ethMatch) {\n    signals.push({\n      symbol: 'ETH',\n      action: ethMatch[1].toUpperCase(),\n      confidence: parseInt(ethMatch[2]),\n      source: 'AI_ANALYSIS',\n      timestamp: new Date().toISOString()\n    });\n  }\n  \n  // Extract BNB signal\n  const bnbMatch = text.match(/BNB Signal:\\s*(BUY|SELL|HOLD).*?Confidence:\\s*(\\d+)%/i);\n  if (bnbMatch) {\n    signals.push({\n      symbol: 'BNB',\n      action: bnbMatch[1].toUpperCase(),\n      confidence: parseInt(bnbMatch[2]),\n      source: 'AI_ANALYSIS',\n      timestamp: new Date().toISOString()\n    });\n  }\n  \n  return signals;\n}\n\n// Extract overall sentiment\nfunction extractSentiment(text) {\n  const sentimentMatch = text.match(/Overall Sentiment:\\s*(BULLISH|BEARISH|NEUTRAL)/i);\n  const confidenceMatch = text.match(/Confidence Level:\\s*(\\d+)%/i);\n  \n  return {\n    sentiment: sentimentMatch ? sentimentMatch[1].toUpperCase() : 'NEUTRAL',\n    confidence: confidenceMatch ? parseInt(confidenceMatch[1]) : 50\n  };\n}\n\n// Extract risk assessment\nfunction extractRisk(text) {\n  const riskMatch = text.match(/Market Risk:\\s*(LOW|MEDIUM|HIGH)/i);\n  const volatilityMatch = text.match(/Volatility:\\s*(LOW|MEDIUM|HIGH)/i);\n  const positionMatch = text.match(/Recommended Position Size:\\s*(\\d+)%/i);\n  \n  return {\n    marketRisk: riskMatch ? riskMatch[1].toUpperCase() : 'MEDIUM',\n    volatility: volatilityMatch ? volatilityMatch[1].toUpperCase() : 'MEDIUM',\n    recommendedPositionSize: positionMatch ? parseInt(positionMatch[1]) : 2\n  };\n}\n\nconst signals = extractSignals(aiResponse);\nconst sentiment = extractSentiment(aiResponse);\nconst risk = extractRisk(aiResponse);\n\n// Filter high-confidence signals (>70%)\nconst highConfidenceSignals = signals.filter(signal => \n  signal.confidence >= 70 && signal.action !== 'HOLD'\n);\n\nconst analysisResult = {\n  timestamp: new Date().toISOString(),\n  aiAnalysis: aiResponse,\n  signals: signals,\n  highConfidenceSignals: highConfidenceSignals,\n  marketSentiment: sentiment,\n  riskAssessment: risk,\n  marketData: marketData,\n  shouldNotify: highConfidenceSignals.length > 0,\n  summary: {\n    totalSignals: signals.length,\n    buySignals: signals.filter(s => s.action === 'BUY').length,\n    sellSignals: signals.filter(s => s.action === 'SELL').length,\n    holdSignals: signals.filter(s => s.action === 'HOLD').length,\n    avgConfidence: signals.length > 0 ? \n      signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length : 0\n  }\n};\n\nreturn [analysisResult];"
      },
      "id": "signal-processor",
      "name": "Signal Processor",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },\n          \"conditions\": [\n            {\n              \"id\": \"high-confidence\",\n              \"leftValue\": \"={{ $json.shouldNotify }}\",\n              \"rightValue\": true,\n              \"operator\": {\n                \"type\": \"boolean\",\n                \"operation\": \"true\"\n              }\n            }\n          ],\n          \"combineOperation\": \"any\"\n        },\n        \"fallbackOutput\": \"extra\"\n      },\n      \"id\": \"notification-filter\",\n      \"name\": \"Notification Filter\",\n      \"type\": \"n8n-nodes-base.switch\",\n      \"typeVersion\": 3,\n      \"position\": [1120, 300]\n    },\n    {\n      \"parameters\": {\n        \"mode\": \"runOnceForAllItems\",\n        \"jsCode\": \"// Generate Telegram notification for high-confidence signals\\nconst analysis = $input.all()[0].json;\\n\\nfunction getSignalEmoji(action) {\\n  switch(action) {\\n    case 'BUY': return '🟢';\\n    case 'SELL': return '🔴';\\n    case 'HOLD': return '🟡';\\n    default: return '⚪';\\n  }\\n}\\n\\nfunction getSentimentEmoji(sentiment) {\\n  switch(sentiment) {\\n    case 'BULLISH': return '🚀';\\n    case 'BEARISH': return '📉';\\n    case 'NEUTRAL': return '➡️';\\n    default: return '🤔';\\n  }\\n}\\n\\nconst message = `🤖 *AI Analiz Raporu*\\n\\n${getSentimentEmoji(analysis.marketSentiment.sentiment)} *Genel Durum:* ${analysis.marketSentiment.sentiment} (${analysis.marketSentiment.confidence}%)\\n\\n🎯 *Yüksek Güvenilirlik Sinyalleri:*\\n${analysis.highConfidenceSignals.map(signal => \\n  `${getSignalEmoji(signal.action)} *${signal.symbol}:* ${signal.action} (${signal.confidence}%)`\\n).join('\\\\n')}\\n\\n📊 *Risk Değerlendirmesi:*\\n• Piyasa Riski: ${analysis.riskAssessment.marketRisk}\\n• Volatilite: ${analysis.riskAssessment.volatility}\\n• Önerilen Pozisyon: %${analysis.riskAssessment.recommendedPositionSize}\\n\\n📈 *Sinyal Özeti:*\\n• Toplam: ${analysis.summary.totalSignals}\\n• Alım: ${analysis.summary.buySignals}\\n• Satım: ${analysis.summary.sellSignals}\\n• Bekle: ${analysis.summary.holdSignals}\\n• Ort. Güven: ${analysis.summary.avgConfidence.toFixed(1)}%\\n\\n⚠️ *Uyarı:* Bu analiz yatırım tavsiyesi değildir!\\n\\n_${new Date().toLocaleString('tr-TR')}_`;\\n\\nreturn [{\\n  json: {\\n    chatId: process.env.TELEGRAM_CHAT_ID,\\n    message: message,\\n    parseMode: 'Markdown',\\n    analysis: analysis\\n  }\\n}];\"\n      },\n      \"id\": \"telegram-notification\",\n      \"name\": \"Telegram Notification\",\n      \"type\": \"n8n-nodes-base.code\",\n      \"typeVersion\": 2,\n      \"position\": [1340, 200]\n    },\n    {\n      \"parameters\": {\n        \"resource\": \"message\",\n        \"operation\": \"sendMessage\",\n        \"chatId\": \"={{ $json.chatId }}\",\n        \"text\": \"={{ $json.message }}\",\n        \"additionalFields\": {\n          \"parse_mode\": \"Markdown\"\n        }\n      },\n      \"id\": \"send-telegram\",\n      \"name\": \"Send Telegram\",\n      \"type\": \"n8n-nodes-base.telegram\",\n      \"typeVersion\": 1.2,\n      \"position\": [1560, 200]\n    },\n    {\n      \"parameters\": {\n        \"mode\": \"runOnceForAllItems\",\n        \"jsCode\": \"// Save analysis to database\\n// In real implementation, this would save to your database\\n\\nconst analysis = $input.all()[0].json;\\n\\nconst dbRecord = {\\n  id: Date.now(),\\n  timestamp: analysis.timestamp,\\n  analysis: analysis,\\n  signals: analysis.signals,\\n  highConfidenceSignals: analysis.highConfidenceSignals,\\n  marketSentiment: analysis.marketSentiment,\\n  riskAssessment: analysis.riskAssessment,\\n  saved_at: new Date().toISOString()\\n};\\n\\n// Log for debugging\\nconsole.log('AI Analysis Saved:', {\\n  timestamp: dbRecord.timestamp,\\n  totalSignals: analysis.summary.totalSignals,\\n  highConfidenceSignals: analysis.highConfidenceSignals.length,\\n  sentiment: analysis.marketSentiment.sentiment,\\n  avgConfidence: analysis.summary.avgConfidence\\n});\\n\\nreturn [{\\n  json: {\\n    success: true,\\n    message: 'AI analysis saved to database',\\n    record_id: dbRecord.id,\\n    signalsGenerated: analysis.summary.totalSignals,\\n    highConfidenceSignals: analysis.highConfidenceSignals.length\\n  }\\n}];\"\n      },\n      \"id\": \"save-analysis\",\n      \"name\": \"Save Analysis\",\n      \"type\": \"n8n-nodes-base.code\",\n      \"typeVersion\": 2,\n      \"position\": [1340, 400]\n    }\n  ],\n  \"connections\": {\n    \"AI Analysis Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Fetch Market Data\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Fetch Market Data\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"OpenAI Analysis\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"OpenAI Analysis\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Signal Processor\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Signal Processor\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Notification Filter\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Notification Filter\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Telegram Notification\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"Save Analysis\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Telegram Notification\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Send Telegram\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Send Telegram\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Save Analysis\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"settings\": {\n    \"executionOrder\": \"v1\"\n  },\n  \"staticData\": null,\n  \"tags\": [\"ai\", \"analysis\", \"trading\", \"signals\"],\n  \"triggerCount\": 1,\n  \"updatedAt\": \"2024-01-20T10:00:00.000Z\",\n  \"versionId\": \"1\"\n}
