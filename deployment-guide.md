# Kripto Trading Sistemi - Kurulum ve Deployment Rehberi

## 🚀 Hızlı Başlangıç

### Ö<PERSON>
- Docker ve Docker Compose
- Binance TR hesabı
- Telegram hesabı
- OpenAI API anahtarı
- PostgreSQL database (opsiyonel)

## 📦 1. Sistem Kurulumu

### Docker ile n8n Kurulumu

```bash
# Proje dizini oluştur
mkdir crypto-trading-bot
cd crypto-trading-bot

# Docker Compose dosyası oluştur
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n-crypto-bot
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=your_secure_password
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678
      - GENERIC_TIMEZONE=Europe/Istanbul
      
      # API Keys
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_SECRET_KEY=${BINANCE_SECRET_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      
      # Risk Management
      - MAX_POSITION_SIZE_PERCENT=5
      - DAILY_LOSS_LIMIT_PERCENT=10
      - STOP_LOSS_PERCENT=3
      - TAKE_PROFIT_PERCENT=8
      
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    container_name: n8n-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    container_name: n8n-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data

volumes:
  n8n_data:
  postgres_data:
  redis_data:
EOF

# Environment dosyası oluştur
cat > .env << 'EOF'
# Binance TR API (https://www.trbinance.com/en/my/settings/api-management)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here

# Telegram Bot (@BotFather'dan alın)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# OpenAI API (https://platform.openai.com/api-keys)
OPENAI_API_KEY=your_openai_api_key_here
EOF

# Sistemi başlat
docker-compose up -d
```

### 2. Telegram Bot Kurulumu

**Adım 1: Bot Oluşturma**
```bash
# Telegram'da @BotFather'a mesaj gönderin
/start
/newbot
# Bot adını girin: Crypto Trading Bot
# Bot username'ini girin: your_crypto_bot

# Bot token'ını .env dosyasına ekleyin
```

**Adım 2: Chat ID Bulma**
```bash
# Bot'unuza mesaj gönderin, sonra:
curl https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates

# Response'dan chat.id değerini alın ve .env'e ekleyin
```

### 3. Workflow'ları İçe Aktarma

```bash
# n8n arayüzüne gidin: http://localhost:5678
# Giriş: admin / your_secure_password

# Her workflow dosyasını içe aktarın:
# 1. workflow-1-market-data-collector.json
# 2. workflow-2-telegram-bot.json  
# 3. workflow-3-ai-analyzer.json
```

## 🔧 3. Konfigürasyon

### Telegram Bot Webhook Ayarı

```bash
# Bot webhook'unu n8n'e yönlendirin
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
  -H "Content-Type: application/json" \
  -d '{"url": "http://your-server-ip:5678/webhook/telegram"}'
```

### Binance API Test

```bash
# n8n'de "Binance API Test" workflow'unu çalıştırın
# Hesap bilgilerinizi görebiliyorsanız API çalışıyor
```

### OpenAI Test

```bash
# n8n'de "AI Market Analyzer" workflow'unu manuel çalıştırın
# AI analiz sonucu alıyorsanız OpenAI çalışıyor
```

## 📊 4. Database Kurulumu (Opsiyonel)

### PostgreSQL Schema

```sql
-- n8n postgres container'ına bağlan
docker exec -it n8n-postgres psql -U n8n -d n8n

-- Tabloları oluştur
CREATE TABLE market_data (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(10) NOT NULL,
  price DECIMAL(18,8) NOT NULL,
  volume DECIMAL(18,8),
  rsi DECIMAL(5,2),
  macd DECIMAL(10,4),
  fear_greed_index INTEGER,
  timestamp TIMESTAMP DEFAULT NOW()
);

CREATE TABLE trading_signals (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(10) NOT NULL,
  signal_type VARCHAR(10) NOT NULL,
  confidence DECIMAL(5,2) NOT NULL,
  price DECIMAL(18,8) NOT NULL,
  reason TEXT,
  source VARCHAR(20) DEFAULT 'AI_ANALYSIS',
  timestamp TIMESTAMP DEFAULT NOW()
);

CREATE TABLE trades (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(10) NOT NULL,
  side VARCHAR(10) NOT NULL,
  quantity DECIMAL(18,8) NOT NULL,
  price DECIMAL(18,8) NOT NULL,
  status VARCHAR(20) DEFAULT 'PENDING',
  order_id VARCHAR(50),
  timestamp TIMESTAMP DEFAULT NOW()
);

CREATE TABLE portfolio_history (
  id SERIAL PRIMARY KEY,
  total_value DECIMAL(18,2) NOT NULL,
  total_pnl DECIMAL(18,2) NOT NULL,
  pnl_percentage DECIMAL(5,2) NOT NULL,
  positions JSONB,
  timestamp TIMESTAMP DEFAULT NOW()
);

-- İndeksler oluştur
CREATE INDEX idx_market_data_symbol_timestamp ON market_data(symbol, timestamp);
CREATE INDEX idx_trading_signals_timestamp ON trading_signals(timestamp);
CREATE INDEX idx_trades_timestamp ON trades(timestamp);
```

## 🔄 5. Workflow Aktivasyonu

### Sıralı Aktivasyon

```bash
# 1. Market Data Collector'ı aktive edin (1 dakika interval)
# 2. AI Market Analyzer'ı aktive edin (5 dakika interval)  
# 3. Telegram Bot'u aktive edin (webhook)
```

### Test Komutları

```bash
# Telegram'da bot'unuza test mesajları gönderin:
/start
/help
/status
/balance
/signals
```

## 📈 6. Monitoring ve Alertler

### Sistem Sağlığı Kontrolü

```bash
# Docker container'ları kontrol et
docker-compose ps

# n8n loglarını izle
docker-compose logs -f n8n

# Database bağlantısını test et
docker exec -it n8n-postgres psql -U n8n -d n8n -c "SELECT NOW();"
```

### Günlük Rapor Workflow'u

```json
{
  "name": "Daily Report",
  "nodes": [
    {
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "parameters": {
        "rule": {
          "interval": [{"field": "hours", "value": 24}]
        }
      }
    },
    {
      "name": "Generate Report",
      "type": "n8n-nodes-base.code",
      "parameters": {
        "jsCode": "// Günlük performans raporu oluştur"
      }
    },
    {
      "name": "Send Report",
      "type": "n8n-nodes-base.telegram"
    }
  ]
}
```

## 🛡️ 7. Güvenlik ve Backup

### Backup Stratejisi

```bash
# Günlük backup scripti
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)

# n8n data backup
docker run --rm -v crypto-trading-bot_n8n_data:/data -v $(pwd)/backups:/backup alpine tar czf /backup/n8n_backup_$DATE.tar.gz -C /data .

# Database backup
docker exec n8n-postgres pg_dump -U n8n n8n > backups/db_backup_$DATE.sql

# Keep only last 7 days
find backups/ -name "*.tar.gz" -mtime +7 -delete
find backups/ -name "*.sql" -mtime +7 -delete
EOF

chmod +x backup.sh

# Crontab'a ekle (günlük 02:00)
echo "0 2 * * * /path/to/backup.sh" | crontab -
```

### SSL/HTTPS Kurulumu (Production)

```bash
# Nginx reverse proxy ile SSL
cat > nginx.conf << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/certs/your-cert.pem;
    ssl_certificate_key /etc/ssl/private/your-key.pem;
    
    location / {
        proxy_pass http://localhost:5678;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF
```

## 🚨 8. Troubleshooting

### Yaygın Sorunlar

**Problem: n8n başlamıyor**
```bash
# Logları kontrol et
docker-compose logs n8n

# Port çakışması kontrolü
netstat -tulpn | grep 5678
```

**Problem: Telegram bot yanıt vermiyor**
```bash
# Webhook durumunu kontrol et
curl "https://api.telegram.org/bot<TOKEN>/getWebhookInfo"

# Webhook'u yeniden ayarla
curl -X POST "https://api.telegram.org/bot<TOKEN>/setWebhook" \
  -d "url=http://your-ip:5678/webhook/telegram"
```

**Problem: Binance API hatası**
```bash
# API anahtarı ve izinleri kontrol et
# IP whitelist kontrolü
# Rate limit kontrolü
```

### Log Analizi

```bash
# n8n execution logları
docker exec -it n8n-crypto-bot cat /home/<USER>/.n8n/logs/n8n.log

# Postgres query logları
docker exec -it n8n-postgres tail -f /var/log/postgresql/postgresql-15-main.log
```

## 🎯 9. Optimizasyon

### Performance Tuning

```bash
# n8n worker sayısını artır
# docker-compose.yml'de:
environment:
  - N8N_WORKERS=4
  - N8N_QUEUE_BULL_REDIS_HOST=redis
```

### Memory Management

```bash
# Container memory limitlerini ayarla
services:
  n8n:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

Bu rehber ile sisteminizi güvenli ve stabil bir şekilde kurabilir, izleyebilir ve optimize edebilirsiniz. Küçük tutarlarla başlayıp sistem performansını gözlemleyerek yatırım miktarını artırmanız önerilir.
