# Binance TR API Entegrasyonu - Güvenlik Rehberi

## 🔐 Güvenlik Öncelikli Kurulum

### 1. Binance TR API Anahtarı Oluşturma

**Adım 1: Binance TR Hesabınıza Giriş Yapın**
- [Binance TR](https://www.trbinance.com) hesabınıza giriş yapın
- Profil → API Management bölümüne gidin

**Adım 2: API Anahtarı Oluşturun**
```
API Key Name: n8n-trading-bot
API Restrictions:
✅ Enable Reading
✅ Enable Spot & Margin Trading
❌ Enable Futures (Başlangıçta kapalı tutun)
❌ Enable Withdrawals (GÜVENLİK İÇİN KAPALI)
```

**Adım 3: IP Whitelist Ayarlayın**
```
Trusted IPs: 
- n8n sunucunuzun IP adresi
- Evinizin IP adresi (test için)
```

### 2. Environment Variables Kurulumu

**n8n Docker Environment:**
```bash
# .env dosyası oluşturun
cat > .env << EOF
# Binance TR API
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
BINANCE_BASE_URL=https://api.binance.com

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# OpenAI
OPENAI_API_KEY=your_openai_key_here

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/crypto_trading

# Risk Management
MAX_POSITION_SIZE_PERCENT=5
DAILY_LOSS_LIMIT_PERCENT=10
STOP_LOSS_PERCENT=3
TAKE_PROFIT_PERCENT=8
EOF
```

### 3. Binance API Test Workflow

```json
{
  "name": "Binance API Test",
  "nodes": [
    {
      "name": "Manual Trigger",
      "type": "n8n-nodes-base.manualTrigger",
      "parameters": {}
    },
    {
      "name": "Account Info",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "https://api.binance.com/api/v3/account",
        "method": "GET",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpHeaderAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "X-MBX-APIKEY",
              "value": "={{ $env.BINANCE_API_KEY }}"
            }
          ]
        },
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "timestamp",
              "value": "={{ Date.now() }}"
            },
            {
              "name": "signature",
              "value": "={{ $node['Generate Signature'].json.signature }}"
            }
          ]
        }
      }
    },
    {
      "name": "Generate Signature",
      "type": "n8n-nodes-base.code",
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "const crypto = require('crypto');\n\nconst timestamp = Date.now();\nconst queryString = `timestamp=${timestamp}`;\nconst signature = crypto\n  .createHmac('sha256', process.env.BINANCE_SECRET_KEY)\n  .update(queryString)\n  .digest('hex');\n\nreturn [{\n  json: {\n    timestamp,\n    queryString,\n    signature\n  }\n}];"
      }
    }
  ]
}
```

### 4. Risk Yönetimi Fonksiyonları

**Position Size Calculator:**
```javascript
function calculatePositionSize(accountBalance, riskPercent, stopLossPercent) {
  const riskAmount = accountBalance * (riskPercent / 100);
  const positionSize = riskAmount / (stopLossPercent / 100);
  return Math.min(positionSize, accountBalance * 0.05); // Max %5 portfolio
}

// Örnek kullanım
const balance = 10000; // TRY
const risk = 2; // %2 risk per trade
const stopLoss = 3; // %3 stop loss
const maxPosition = calculatePositionSize(balance, risk, stopLoss);
console.log(`Max Position Size: ${maxPosition} TRY`);
```

**Daily Loss Tracker:**
```javascript
function checkDailyLoss(todaysPnL, accountBalance, maxLossPercent = 10) {
  const maxLoss = accountBalance * (maxLossPercent / 100);
  const currentLossPercent = Math.abs(todaysPnL) / accountBalance * 100;
  
  return {
    canTrade: currentLossPercent < maxLossPercent,
    currentLoss: currentLossPercent,
    maxLoss: maxLossPercent,
    remainingLoss: maxLossPercent - currentLossPercent
  };
}
```

### 5. Binance Trading Workflow

```json
{
  "name": "Binance Trading Executor",
  "nodes": [
    {
      "name": "Signal Trigger",
      "type": "n8n-nodes-base.webhook",
      "parameters": {
        "path": "trading-signal"
      }
    },
    {
      "name": "Validate Signal",
      "type": "n8n-nodes-base.code",
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Signal validation\nconst signal = $input.all()[0].json;\n\n// Required fields check\nif (!signal.symbol || !signal.action || !signal.confidence) {\n  throw new Error('Invalid signal format');\n}\n\n// Confidence threshold\nif (signal.confidence < 75) {\n  throw new Error('Signal confidence too low');\n}\n\n// Allowed symbols\nconst allowedSymbols = ['BTCTRY', 'ETHTRY', 'BNBTRY'];\nif (!allowedSymbols.includes(signal.symbol)) {\n  throw new Error('Symbol not allowed');\n}\n\nreturn [signal];"
      }
    },
    {
      "name": "Get Account Balance",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "https://api.binance.com/api/v3/account",
        "method": "GET"
      }
    },
    {
      "name": "Calculate Position Size",
      "type": "n8n-nodes-base.code",
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Position sizing with risk management\nconst signal = $input.all()[0].json;\nconst account = $input.all()[1].json;\n\n// Find TRY balance\nconst tryBalance = account.balances.find(b => b.asset === 'TRY');\nconst availableBalance = parseFloat(tryBalance.free);\n\n// Risk parameters\nconst riskPerTrade = 0.02; // 2%\nconst maxPositionPercent = 0.05; // 5%\nconst stopLossPercent = 0.03; // 3%\n\n// Calculate position size\nconst riskAmount = availableBalance * riskPerTrade;\nconst positionSize = riskAmount / stopLossPercent;\nconst maxPosition = availableBalance * maxPositionPercent;\n\nconst finalPositionSize = Math.min(positionSize, maxPosition);\n\nreturn [{\n  json: {\n    signal,\n    account,\n    positionSize: finalPositionSize,\n    availableBalance,\n    riskAmount\n  }\n}];"
      }
    },
    {
      "name": "Place Order",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "https://api.binance.com/api/v3/order",
        "method": "POST",
        "sendBody": true,
        "contentType": "form-urlencoded",
        "bodyParameters": {
          "parameters": [
            {
              "name": "symbol",
              "value": "={{ $json.signal.symbol }}"
            },
            {
              "name": "side",
              "value": "={{ $json.signal.action }}"
            },
            {
              "name": "type",
              "value": "MARKET"
            },
            {
              "name": "quoteOrderQty",
              "value": "={{ $json.positionSize }}"
            },
            {
              "name": "timestamp",
              "value": "={{ Date.now() }}"
            }
          ]
        }
      }
    },
    {
      "name": "Set Stop Loss",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "https://api.binance.com/api/v3/order",
        "method": "POST"
      }
    },
    {
      "name": "Telegram Notification",
      "type": "n8n-nodes-base.telegram",
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "={{ $env.TELEGRAM_CHAT_ID }}",
        "text": "🎯 İşlem Gerçekleştirildi!\n\n📊 Sembol: {{ $json.signal.symbol }}\n🔄 İşlem: {{ $json.signal.action }}\n💰 Tutar: {{ $json.positionSize }} TRY\n📈 Güven: {{ $json.signal.confidence }}%\n\n⏰ {{ new Date().toLocaleString('tr-TR') }}"
      }
    }
  ]
}
```

### 6. Güvenlik Kontrol Listesi

**✅ API Güvenliği:**
- [ ] API anahtarları environment variables'da
- [ ] IP whitelist aktif
- [ ] Withdrawal permissions kapalı
- [ ] API key rotation planı (aylık)

**✅ Risk Yönetimi:**
- [ ] Maksimum pozisyon büyüklüğü: %5
- [ ] Günlük zarar limiti: %10
- [ ] Stop-loss otomatik: %3
- [ ] Take-profit otomatik: %8

**✅ Monitoring:**
- [ ] Telegram bildirimleri aktif
- [ ] Error handling ve logging
- [ ] Daily P&L tracking
- [ ] API rate limit monitoring

### 7. Test Senaryoları

**Test 1: API Bağlantısı**
```bash
curl -X GET "https://api.binance.com/api/v3/ping"
```

**Test 2: Hesap Bilgisi**
```bash
# n8n workflow ile test edin
# Account Info workflow'unu çalıştırın
```

**Test 3: Küçük Test İşlemi**
```javascript
// 10 TRY ile test işlemi
const testOrder = {
  symbol: 'BTCTRY',
  side: 'BUY',
  type: 'MARKET',
  quoteOrderQty: 10
};
```

### 8. Acil Durum Prosedürleri

**Bot Durdurma:**
```bash
# Telegram komutu
/stop

# Manuel durdurma
# n8n workflow'larını deaktive edin
```

**API Anahtarı İptal Etme:**
1. Binance TR → API Management
2. İlgili API anahtarını sil
3. Yeni anahtar oluştur
4. Environment variables güncelle

**Pozisyonları Kapatma:**
```bash
# Telegram komutu
/sell BTC 100%
/sell ETH 100%
/sell BNB 100%
```

### 9. Performans İzleme

**Günlük Rapor:**
- Toplam işlem sayısı
- Kazanan/Kaybeden işlem oranı
- Günlük P&L
- API kullanım istatistikleri

**Haftalık Analiz:**
- Portfolio performansı
- Risk metriklerinin gözden geçirilmesi
- AI sinyal doğruluğu
- Sistem optimizasyonu önerileri

Bu rehber, güvenli ve kontrollü bir şekilde Binance TR API entegrasyonu yapmanızı sağlar. Küçük tutarlarla başlayıp, sistem performansını gözlemleyerek yatırım miktarını artırabilirsiniz.
