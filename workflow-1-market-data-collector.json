{"name": "Market Data Collector", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "value": 1}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 300]}, {"parameters": {"resource": "coin", "operation": "price", "searchBy": "coinId", "coinId": "bitcoin", "quoteCurrencies": ["usd", "try"]}, "id": "coingecko-btc", "name": "CoinGecko BTC", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [460, 200]}, {"parameters": {"resource": "coin", "operation": "price", "searchBy": "coinId", "coinId": "ethereum", "quoteCurrencies": ["usd", "try"]}, "id": "coingecko-eth", "name": "CoinGecko ETH", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"resource": "coin", "operation": "price", "searchBy": "coinId", "coinId": "binancecoin", "quoteCurrencies": ["usd", "try"]}, "id": "coingecko-bnb", "name": "CoinGecko BNB", "type": "n8n-nodes-base.coinGecko", "typeVersion": 1, "position": [460, 400]}, {"parameters": {"url": "https://api.binance.com/api/v3/ticker/24hr", "method": "GET", "sendQuery": true, "queryParameters": {"parameters": [{"name": "symbols", "value": "[\"BTCTRY\",\"ETHTRY\",\"BNBTRY\"]"}]}}, "id": "binance-api", "name": "Binance TR API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 300]}, {"parameters": {"url": "https://api.alternative.me/fng/", "method": "GET"}, "id": "fear-greed-index", "name": "Fear & Greed Index", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 400]}, {"parameters": {"mode": "runOnceForAllItems", "jsCode": "// Market Data Processing and Technical Analysis\nconst items = $input.all();\n\n// Combine all market data\nlet marketData = {\n  timestamp: new Date().toISOString(),\n  prices: {},\n  binanceData: {},\n  fearGreedIndex: null,\n  technicalIndicators: {}\n};\n\n// Process CoinGecko data\nitems.forEach(item => {\n  if (item.json.bitcoin) {\n    marketData.prices.BTC = {\n      usd: item.json.bitcoin.usd,\n      try: item.json.bitcoin.try,\n      source: 'coingecko'\n    };\n  }\n  if (item.json.ethereum) {\n    marketData.prices.ETH = {\n      usd: item.json.ethereum.usd,\n      try: item.json.ethereum.try,\n      source: 'coingecko'\n    };\n  }\n  if (item.json.binancecoin) {\n    marketData.prices.BNB = {\n      usd: item.json.binancecoin.usd,\n      try: item.json.binancecoin.try,\n      source: 'coingecko'\n    };\n  }\n  \n  // Process Binance data\n  if (Array.isArray(item.json)) {\n    item.json.forEach(ticker => {\n      const symbol = ticker.symbol;\n      marketData.binanceData[symbol] = {\n        price: parseFloat(ticker.lastPrice),\n        change24h: parseFloat(ticker.priceChangePercent),\n        volume: parseFloat(ticker.volume),\n        high24h: parseFloat(ticker.highPrice),\n        low24h: parseFloat(ticker.lowPrice)\n      };\n    });\n  }\n  \n  // Process Fear & Greed Index\n  if (item.json.data && item.json.data[0]) {\n    marketData.fearGreedIndex = {\n      value: parseInt(item.json.data[0].value),\n      classification: item.json.data[0].value_classification,\n      timestamp: item.json.data[0].timestamp\n    };\n  }\n});\n\n// Technical Analysis Functions\nfunction calculateRSI(prices, period = 14) {\n  if (prices.length < period + 1) return null;\n  \n  let gains = 0;\n  let losses = 0;\n  \n  for (let i = 1; i <= period; i++) {\n    const change = prices[i] - prices[i - 1];\n    if (change > 0) gains += change;\n    else losses -= change;\n  }\n  \n  const avgGain = gains / period;\n  const avgLoss = losses / period;\n  const rs = avgGain / avgLoss;\n  const rsi = 100 - (100 / (1 + rs));\n  \n  return rsi;\n}\n\nfunction calculateSMA(prices, period) {\n  if (prices.length < period) return null;\n  const sum = prices.slice(-period).reduce((a, b) => a + b, 0);\n  return sum / period;\n}\n\nfunction calculateEMA(prices, period) {\n  if (prices.length < period) return null;\n  \n  const multiplier = 2 / (period + 1);\n  let ema = prices.slice(0, period).reduce((a, b) => a + b, 0) / period;\n  \n  for (let i = period; i < prices.length; i++) {\n    ema = (prices[i] * multiplier) + (ema * (1 - multiplier));\n  }\n  \n  return ema;\n}\n\n// Mock historical prices for demo (in real implementation, fetch from database)\nconst mockBTCPrices = [\n  45000, 45200, 44800, 45500, 46000, 45800, 46200, 46500, 46300, 46800,\n  47000, 46700, 47200, 47500, 47300, 47800, 48000, 47600, 48200, 48500\n];\n\n// Add current price\nif (marketData.prices.BTC) {\n  mockBTCPrices.push(marketData.prices.BTC.usd);\n}\n\n// Calculate technical indicators\nmarketData.technicalIndicators = {\n  BTC: {\n    RSI: calculateRSI(mockBTCPrices),\n    SMA_20: calculateSMA(mockBTCPrices, 20),\n    EMA_12: calculateEMA(mockBTCPrices, 12),\n    EMA_26: calculateEMA(mockBTCPrices, 26)\n  }\n};\n\n// Calculate MACD\nif (marketData.technicalIndicators.BTC.EMA_12 && marketData.technicalIndicators.BTC.EMA_26) {\n  marketData.technicalIndicators.BTC.MACD = \n    marketData.technicalIndicators.BTC.EMA_12 - marketData.technicalIndicators.BTC.EMA_26;\n}\n\n// Generate trading signals based on technical analysis\nfunction generateSignals(data) {\n  const signals = [];\n  const btcIndicators = data.technicalIndicators.BTC;\n  const btcPrice = data.prices.BTC?.usd;\n  \n  if (!btcIndicators || !btcPrice) return signals;\n  \n  // RSI signals\n  if (btcIndicators.RSI) {\n    if (btcIndicators.RSI < 30) {\n      signals.push({\n        type: 'BUY',\n        reason: 'RSI Oversold',\n        confidence: 70,\n        symbol: 'BTC',\n        price: btcPrice\n      });\n    } else if (btcIndicators.RSI > 70) {\n      signals.push({\n        type: 'SELL',\n        reason: 'RSI Overbought',\n        confidence: 70,\n        symbol: 'BTC',\n        price: btcPrice\n      });\n    }\n  }\n  \n  // MACD signals\n  if (btcIndicators.MACD) {\n    if (btcIndicators.MACD > 0) {\n      signals.push({\n        type: 'BUY',\n        reason: 'MACD Bullish',\n        confidence: 60,\n        symbol: 'BTC',\n        price: btcPrice\n      });\n    } else {\n      signals.push({\n        type: 'SELL',\n        reason: 'MACD Bearish',\n        confidence: 60,\n        symbol: 'BTC',\n        price: btcPrice\n      });\n    }\n  }\n  \n  // Fear & Greed signals\n  if (data.fearGreedIndex) {\n    if (data.fearGreedIndex.value < 25) {\n      signals.push({\n        type: 'BUY',\n        reason: 'Extreme Fear',\n        confidence: 80,\n        symbol: 'MARKET',\n        price: btcPrice\n      });\n    } else if (data.fearGreedIndex.value > 75) {\n      signals.push({\n        type: 'SELL',\n        reason: 'Extreme Greed',\n        confidence: 80,\n        symbol: 'MARKET',\n        price: btcPrice\n      });\n    }\n  }\n  \n  return signals;\n}\n\nmarketData.signals = generateSignals(marketData);\n\n// Calculate overall market sentiment\nfunction calculateMarketSentiment(data) {\n  let bullishSignals = 0;\n  let bearishSignals = 0;\n  let totalConfidence = 0;\n  \n  data.signals.forEach(signal => {\n    if (signal.type === 'BUY') {\n      bullishSignals++;\n      totalConfidence += signal.confidence;\n    } else if (signal.type === 'SELL') {\n      bearishSignals++;\n      totalConfidence += signal.confidence;\n    }\n  });\n  \n  const totalSignals = bullishSignals + bearishSignals;\n  const avgConfidence = totalSignals > 0 ? totalConfidence / totalSignals : 0;\n  \n  let sentiment = 'NEUTRAL';\n  if (bullishSignals > bearishSignals) sentiment = 'BULLISH';\n  else if (bearishSignals > bullishSignals) sentiment = 'BEARISH';\n  \n  return {\n    sentiment,\n    confidence: avgConfidence,\n    bullishSignals,\n    bearishSignals,\n    totalSignals\n  };\n}\n\nmarketData.marketSentiment = calculateMarketSentiment(marketData);\n\nreturn [marketData];"}, "id": "technical-analysis", "name": "Technical Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"mode": "runOnceForAllItems", "jsCode": "// Database Storage Simulation\n// In real implementation, this would save to PostgreSQL/MongoDB\n\nconst marketData = $input.all()[0].json;\n\n// Simulate database save\nconst dbRecord = {\n  id: Date.now(),\n  timestamp: marketData.timestamp,\n  data: marketData,\n  saved_at: new Date().toISOString()\n};\n\n// Log for debugging\nconsole.log('Market Data Saved:', {\n  timestamp: dbRecord.timestamp,\n  btc_price: marketData.prices.BTC?.usd,\n  signals_count: marketData.signals?.length || 0,\n  sentiment: marketData.marketSentiment?.sentiment\n});\n\nreturn [{\n  json: {\n    success: true,\n    message: 'Market data processed and saved',\n    record_id: dbRecord.id,\n    signals: marketData.signals,\n    sentiment: marketData.marketSentiment\n  }\n}];"}, "id": "database-save", "name": "Database Save", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}], "connections": {"Schedule Trigger": {"main": [[{"node": "CoinGecko BTC", "type": "main", "index": 0}, {"node": "CoinGecko ETH", "type": "main", "index": 0}, {"node": "CoinGecko BNB", "type": "main", "index": 0}, {"node": "Binance TR API", "type": "main", "index": 0}, {"node": "Fear & Greed Index", "type": "main", "index": 0}]]}, "CoinGecko BTC": {"main": [[{"node": "Technical Analysis", "type": "main", "index": 0}]]}, "CoinGecko ETH": {"main": [[{"node": "Technical Analysis", "type": "main", "index": 0}]]}, "CoinGecko BNB": {"main": [[{"node": "Technical Analysis", "type": "main", "index": 0}]]}, "Binance TR API": {"main": [[{"node": "Technical Analysis", "type": "main", "index": 0}]]}, "Fear & Greed Index": {"main": [[{"node": "Technical Analysis", "type": "main", "index": 0}]]}, "Technical Analysis": {"main": [[{"node": "Database Save", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": ["crypto", "trading", "market-data"], "triggerCount": 1, "updatedAt": "2024-01-20T10:00:00.000Z", "versionId": "1"}