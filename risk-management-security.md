# Risk Yönetimi ve Güvenlik Protokolleri

## 🛡️ Güvenlik Katmanları

### 1. API Güvenliği

**Binance TR API Güvenlik Önlemleri:**
```javascript
// API Key Rotation (Aylık)
const rotateApiKeys = async () => {
  // 1. Yeni API key oluştur
  // 2. Eski key'i deaktive et
  // 3. Environment variables güncelle
  // 4. Sistem restart
};

// Rate Limiting
const apiLimiter = {
  requests: 0,
  lastReset: Date.now(),
  maxRequests: 1200, // Binance limit: 1200/minute
  
  checkLimit() {
    const now = Date.now();
    if (now - this.lastReset > 60000) {
      this.requests = 0;
      this.lastReset = now;
    }
    
    if (this.requests >= this.maxRequests) {
      throw new Error('API rate limit exceeded');
    }
    
    this.requests++;
  }
};

// IP Whitelist Validation
const validateIP = (requestIP) => {
  const allowedIPs = process.env.ALLOWED_IPS.split(',');
  if (!allowedIPs.includes(requestIP)) {
    throw new Error('Unauthorized IP address');
  }
};
```

**Telegram Bot Güvenliği:**
```javascript
// User Authentication
const authorizedUsers = [
  'inkbytefo', // Your username
  // Add more authorized users
];

const authenticateUser = (username, chatId) => {
  const isAuthorized = authorizedUsers.includes(username) || 
                      chatId.toString() === process.env.TELEGRAM_CHAT_ID;
  
  if (!isAuthorized) {
    throw new Error('Unauthorized user');
  }
};

// Command Validation
const validateCommand = (command) => {
  const allowedCommands = [
    'status', 'balance', 'signals', 'buy', 'sell', 
    'stop', 'start', 'help', 'analyze'
  ];
  
  if (!allowedCommands.includes(command)) {
    throw new Error('Invalid command');
  }
};
```

### 2. Risk Yönetimi Parametreleri

**Position Sizing:**
```javascript
class RiskManager {
  constructor() {
    this.maxPositionPercent = 5; // %5 max position
    this.maxDailyLossPercent = 10; // %10 günlük zarar limiti
    this.stopLossPercent = 3; // %3 stop-loss
    this.takeProfitPercent = 8; // %8 take-profit
    this.riskPerTradePercent = 2; // %2 risk per trade
  }
  
  calculatePositionSize(accountBalance, entryPrice, stopLossPrice) {
    // Risk-based position sizing
    const riskAmount = accountBalance * (this.riskPerTradePercent / 100);
    const priceRisk = Math.abs(entryPrice - stopLossPrice) / entryPrice;
    const positionSize = riskAmount / priceRisk;
    
    // Apply maximum position limit
    const maxPosition = accountBalance * (this.maxPositionPercent / 100);
    
    return Math.min(positionSize, maxPosition);
  }
  
  checkDailyLoss(todaysPnL, accountBalance) {
    const lossPercent = Math.abs(todaysPnL) / accountBalance * 100;
    
    if (lossPercent >= this.maxDailyLossPercent) {
      return {
        canTrade: false,
        reason: 'Daily loss limit exceeded',
        currentLoss: lossPercent,
        maxLoss: this.maxDailyLossPercent
      };
    }
    
    return {
      canTrade: true,
      remainingLoss: this.maxDailyLossPercent - lossPercent
    };
  }
  
  validateSignal(signal) {
    // Signal validation rules
    const rules = [
      signal.confidence >= 75, // Min %75 confidence
      ['BUY', 'SELL', 'HOLD'].includes(signal.action),
      ['BTCTRY', 'ETHTRY', 'BNBTRY'].includes(signal.symbol),
      signal.timestamp && new Date(signal.timestamp) > new Date(Date.now() - 300000) // Max 5 min old
    ];
    
    return rules.every(rule => rule === true);
  }
}
```

### 3. Otomatik Stop-Loss ve Take-Profit

**Stop-Loss Implementation:**
```javascript
const setStopLoss = async (symbol, quantity, entryPrice, stopLossPercent) => {
  const stopPrice = entryPrice * (1 - stopLossPercent / 100);
  
  const stopLossOrder = {
    symbol: symbol,
    side: 'SELL',
    type: 'STOP_LOSS_LIMIT',
    quantity: quantity,
    price: stopPrice * 0.99, // Limit price slightly below stop price
    stopPrice: stopPrice,
    timeInForce: 'GTC'
  };
  
  try {
    const response = await binanceAPI.newOrder(stopLossOrder);
    console.log('Stop-loss set:', response);
    return response;
  } catch (error) {
    console.error('Stop-loss error:', error);
    throw error;
  }
};

const setTakeProfit = async (symbol, quantity, entryPrice, takeProfitPercent) => {
  const takeProfitPrice = entryPrice * (1 + takeProfitPercent / 100);
  
  const takeProfitOrder = {
    symbol: symbol,
    side: 'SELL',
    type: 'LIMIT',
    quantity: quantity,
    price: takeProfitPrice,
    timeInForce: 'GTC'
  };
  
  try {
    const response = await binanceAPI.newOrder(takeProfitOrder);
    console.log('Take-profit set:', response);
    return response;
  } catch (error) {
    console.error('Take-profit error:', error);
    throw error;
  }
};
```

### 4. Portfolio Monitoring

**Real-time Portfolio Tracking:**
```javascript
class PortfolioMonitor {
  constructor() {
    this.positions = new Map();
    this.dailyPnL = 0;
    this.totalInvested = 0;
    this.lastUpdate = Date.now();
  }
  
  async updatePortfolio() {
    try {
      const account = await binanceAPI.account();
      const balances = account.balances.filter(b => parseFloat(b.free) > 0 || parseFloat(b.locked) > 0);
      
      let totalValue = 0;
      const positions = [];
      
      for (const balance of balances) {
        if (balance.asset === 'TRY') {
          totalValue += parseFloat(balance.free) + parseFloat(balance.locked);
        } else {
          const ticker = await binanceAPI.ticker24hr({ symbol: `${balance.asset}TRY` });
          const value = (parseFloat(balance.free) + parseFloat(balance.locked)) * parseFloat(ticker.lastPrice);
          totalValue += value;
          
          positions.push({
            asset: balance.asset,
            amount: parseFloat(balance.free) + parseFloat(balance.locked),
            value: value,
            price: parseFloat(ticker.lastPrice)
          });
        }
      }
      
      this.calculatePnL(totalValue);
      this.positions = new Map(positions.map(p => [p.asset, p]));
      this.lastUpdate = Date.now();
      
      return {
        totalValue,
        positions,
        dailyPnL: this.dailyPnL,
        totalPnL: totalValue - this.totalInvested
      };
    } catch (error) {
      console.error('Portfolio update error:', error);
      throw error;
    }
  }
  
  calculatePnL(currentValue) {
    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);
    
    // Get portfolio value at start of day from database
    // Calculate daily P&L
    // This is a simplified version
    this.dailyPnL = currentValue - this.totalInvested; // Simplified
  }
  
  checkRiskLimits() {
    const riskManager = new RiskManager();
    const account = this.getTotalValue();
    
    return riskManager.checkDailyLoss(this.dailyPnL, account);
  }
}
```

### 5. Emergency Procedures

**Circuit Breaker:**
```javascript
class CircuitBreaker {
  constructor() {
    this.isTripped = false;
    this.tripReasons = [];
    this.lastTrip = null;
  }
  
  checkConditions(portfolio, marketData) {
    const conditions = [
      {
        name: 'Daily Loss Limit',
        condition: Math.abs(portfolio.dailyPnL) / portfolio.totalValue > 0.10,
        action: 'STOP_ALL_TRADING'
      },
      {
        name: 'API Error Rate',
        condition: this.getApiErrorRate() > 0.20,
        action: 'PAUSE_TRADING'
      },
      {
        name: 'Market Volatility',
        condition: marketData.volatility > 0.15,
        action: 'REDUCE_POSITION_SIZE'
      },
      {
        name: 'Consecutive Losses',
        condition: this.getConsecutiveLosses() >= 5,
        action: 'PAUSE_TRADING'
      }
    ];
    
    const triggeredConditions = conditions.filter(c => c.condition);
    
    if (triggeredConditions.length > 0) {
      this.trip(triggeredConditions);
    }
  }
  
  trip(reasons) {
    this.isTripped = true;
    this.tripReasons = reasons;
    this.lastTrip = new Date();
    
    // Send emergency notification
    this.sendEmergencyNotification(reasons);
    
    // Execute emergency actions
    reasons.forEach(reason => {
      this.executeEmergencyAction(reason.action);
    });
  }
  
  async executeEmergencyAction(action) {
    switch (action) {
      case 'STOP_ALL_TRADING':
        await this.stopAllTrading();
        break;
      case 'PAUSE_TRADING':
        await this.pauseTrading(30); // 30 minutes
        break;
      case 'REDUCE_POSITION_SIZE':
        await this.reducePositionSizes(0.5); // 50% reduction
        break;
    }
  }
  
  async sendEmergencyNotification(reasons) {
    const message = `🚨 EMERGENCY STOP 🚨\n\nReasons:\n${reasons.map(r => `• ${r.name}`).join('\n')}\n\nTime: ${new Date().toLocaleString('tr-TR')}\n\nImmediate action required!`;
    
    // Send to Telegram
    await telegramAPI.sendMessage({
      chat_id: process.env.TELEGRAM_CHAT_ID,
      text: message,
      parse_mode: 'Markdown'
    });
    
    // Send email notification (if configured)
    // Log to database
  }
}
```

### 6. Audit ve Compliance

**Trade Logging:**
```javascript
class AuditLogger {
  async logTrade(trade) {
    const auditRecord = {
      timestamp: new Date().toISOString(),
      tradeId: trade.orderId,
      symbol: trade.symbol,
      side: trade.side,
      quantity: trade.executedQty,
      price: trade.price,
      commission: trade.commission,
      signal: trade.originalSignal,
      riskMetrics: {
        positionSize: trade.positionSize,
        riskAmount: trade.riskAmount,
        stopLoss: trade.stopLoss,
        takeProfit: trade.takeProfit
      },
      portfolioSnapshot: await this.getPortfolioSnapshot()
    };
    
    // Save to database
    await this.saveAuditRecord(auditRecord);
    
    // Generate compliance report if needed
    if (this.shouldGenerateComplianceReport()) {
      await this.generateComplianceReport();
    }
  }
  
  async generateDailyReport() {
    const today = new Date().toISOString().split('T')[0];
    const trades = await this.getTradesToday();
    
    const report = {
      date: today,
      totalTrades: trades.length,
      winningTrades: trades.filter(t => t.pnl > 0).length,
      losingTrades: trades.filter(t => t.pnl < 0).length,
      totalPnL: trades.reduce((sum, t) => sum + t.pnl, 0),
      largestWin: Math.max(...trades.map(t => t.pnl)),
      largestLoss: Math.min(...trades.map(t => t.pnl)),
      riskMetrics: this.calculateRiskMetrics(trades)
    };
    
    return report;
  }
}
```

### 7. Backup ve Recovery

**Data Backup Strategy:**
```bash
#!/bin/bash
# backup-strategy.sh

# 1. Database Backup
pg_dump -h localhost -U n8n -d n8n > "backup_$(date +%Y%m%d_%H%M%S).sql"

# 2. n8n Workflows Backup
docker exec n8n-crypto-bot tar czf - /home/<USER>/.n8n/workflows > "workflows_$(date +%Y%m%d_%H%M%S).tar.gz"

# 3. Environment Variables Backup (encrypted)
gpg --symmetric --cipher-algo AES256 .env

# 4. Upload to secure cloud storage
aws s3 cp backup_*.sql s3://your-backup-bucket/database/
aws s3 cp workflows_*.tar.gz s3://your-backup-bucket/workflows/
aws s3 cp .env.gpg s3://your-backup-bucket/config/

# 5. Cleanup old backups (keep 30 days)
find . -name "backup_*.sql" -mtime +30 -delete
find . -name "workflows_*.tar.gz" -mtime +30 -delete
```

**Disaster Recovery Plan:**
```markdown
## Disaster Recovery Checklist

### Immediate Actions (0-15 minutes)
1. [ ] Stop all trading activities
2. [ ] Secure API keys
3. [ ] Assess system damage
4. [ ] Notify stakeholders

### Short-term Recovery (15-60 minutes)
1. [ ] Restore from latest backup
2. [ ] Verify data integrity
3. [ ] Test critical functions
4. [ ] Resume monitoring

### Long-term Recovery (1-24 hours)
1. [ ] Full system audit
2. [ ] Performance optimization
3. [ ] Update security measures
4. [ ] Document lessons learned
```

Bu kapsamlı risk yönetimi ve güvenlik protokolleri, sisteminizin güvenli ve kontrollü bir şekilde çalışmasını sağlar. Düzenli olarak gözden geçirilmeli ve güncellenmelidir.
