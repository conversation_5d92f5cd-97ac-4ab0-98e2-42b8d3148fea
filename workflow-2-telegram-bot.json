{
  "name": "Telegram Trading Bot",
  "nodes": [
    {
      "parameters": {
        "updates": ["message"]
      },
      "id": "telegram-trigger",
      "name": "Telegram Trigger",
      "type": "n8n-nodes-base.telegramTrigger",
      "typeVersion": 1.2,
      "position": [240, 300]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Command Parser and User Authentication\nconst message = $input.all()[0].json.message;\nconst chatId = message.chat.id;\nconst userId = message.from.id;\nconst username = message.from.username || message.from.first_name;\nconst text = message.text || '';\n\n// Authorized users (in real implementation, store in database)\nconst authorizedUsers = [\n  'inkbytefo', // Your Telegram username\n  // Add more authorized users here\n];\n\n// Check if user is authorized\nconst isAuthorized = authorizedUsers.includes(username) || chatId.toString() === process.env.TELEGRAM_CHAT_ID;\n\nif (!isAuthorized) {\n  return [{\n    json: {\n      authorized: false,\n      chatId,\n      response: '🚫 Yetkisiz erişim! Bu bot sadece yetkili kullanıcılar tarafından kullanılabilir.'\n    }\n  }];\n}\n\n// Parse command\nconst command = text.toLowerCase().trim();\nlet parsedCommand = {\n  action: 'unknown',\n  params: [],\n  rawText: text,\n  chatId,\n  userId,\n  username\n};\n\nif (command.startsWith('/')) {\n  const parts = command.split(' ');\n  const cmd = parts[0].substring(1); // Remove '/'\n  const params = parts.slice(1);\n  \n  parsedCommand = {\n    action: cmd,\n    params,\n    rawText: text,\n    chatId,\n    userId,\n    username,\n    authorized: true\n  };\n} else {\n  // Handle natural language commands\n  if (command.includes('fiyat') || command.includes('price')) {\n    parsedCommand.action = 'status';\n  } else if (command.includes('al') || command.includes('buy')) {\n    parsedCommand.action = 'buy';\n  } else if (command.includes('sat') || command.includes('sell')) {\n    parsedCommand.action = 'sell';\n  } else if (command.includes('analiz') || command.includes('analysis')) {\n    parsedCommand.action = 'analyze';\n  }\n}\n\nreturn [parsedCommand];"
      },
      "id": "command-parser",
      "name": "Command Parser",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "status",
              "leftValue": "={{ $json.action }}",
              "rightValue": "status",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "balance",
              "leftValue": "={{ $json.action }}",
              "rightValue": "balance",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "buy",
              "leftValue": "={{ $json.action }}",
              "rightValue": "buy",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "sell",
              "leftValue": "={{ $json.action }}",
              "rightValue": "sell",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "signals",
              "leftValue": "={{ $json.action }}",
              "rightValue": "signals",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "analyze",
              "leftValue": "={{ $json.action }}",
              "rightValue": "analyze",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "start",
              "leftValue": "={{ $json.action }}",
              "rightValue": "start",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "id": "help",
              "leftValue": "={{ $json.action }}",
              "rightValue": "help",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combineOperation": "any"
        },
        "fallbackOutput": "extra"
      },
      "id": "command-switch",
      "name": "Command Switch",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 3,
      "position": [680, 300]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Portfolio Status Handler\nconst command = $input.all()[0].json;\n\n// Mock portfolio data (in real implementation, fetch from database)\nconst portfolio = {\n  totalValue: 5250.75, // TRY\n  totalInvested: 5000,\n  totalPnL: 250.75,\n  pnlPercentage: 5.015,\n  positions: [\n    {\n      symbol: 'BTC',\n      amount: 0.00234,\n      value: 3150.50,\n      pnl: 150.50,\n      pnlPercentage: 5.02\n    },\n    {\n      symbol: 'ETH',\n      amount: 0.0876,\n      value: 1800.25,\n      pnl: 75.25,\n      pnlPercentage: 4.36\n    },\n    {\n      symbol: 'BNB',\n      amount: 0.654,\n      value: 300.00,\n      pnl: 25.00,\n      pnlPercentage: 9.09\n    }\n  ]\n};\n\n// Current prices (mock data)\nconst currentPrices = {\n  BTC: { try: 1346789, usd: 48250 },\n  ETH: { try: 82456, usd: 2950 },\n  BNB: { try: 15678, usd: 560 }\n};\n\nconst response = `📊 *Portfolio Durumu*\n\n💰 *Toplam Değer:* ${portfolio.totalValue.toLocaleString('tr-TR')} TRY\n💵 *Yatırılan:* ${portfolio.totalInvested.toLocaleString('tr-TR')} TRY\n${portfolio.pnlPercentage >= 0 ? '📈' : '📉'} *P&L:* ${portfolio.pnlPercentage >= 0 ? '+' : ''}${portfolio.pnlPercentage.toFixed(2)}% (${portfolio.pnlPercentage >= 0 ? '+' : ''}${portfolio.totalPnL.toFixed(2)} TRY)\n\n*Pozisyonlar:*\n${portfolio.positions.map(pos => \n  `🪙 *${pos.symbol}:* ${pos.amount} (${pos.value.toLocaleString('tr-TR')} TRY) ${pos.pnlPercentage >= 0 ? '📈' : '📉'} ${pos.pnlPercentage >= 0 ? '+' : ''}${pos.pnlPercentage.toFixed(2)}%`\n).join('\\n')}\n\n*Güncel Fiyatlar:*\n₿ *Bitcoin:* ${currentPrices.BTC.try.toLocaleString('tr-TR')} TRY\n⟠ *Ethereum:* ${currentPrices.ETH.try.toLocaleString('tr-TR')} TRY\n🔸 *BNB:* ${currentPrices.BNB.try.toLocaleString('tr-TR')} TRY\n\n_Son güncelleme: ${new Date().toLocaleString('tr-TR')}_`;\n\nreturn [{\n  json: {\n    chatId: command.chatId,\n    response,\n    parseMode: 'Markdown'\n  }\n}];"
      },
      "id": "status-handler",
      "name": "Status Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 100]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Balance Handler\nconst command = $input.all()[0].json;\n\n// Mock balance data (in real implementation, fetch from Binance API)\nconst balance = {\n  totalTRY: 1250.75,\n  availableTRY: 850.25,\n  inOrdersTRY: 400.50,\n  cryptoBalances: [\n    { asset: 'BTC', free: 0.00234, locked: 0.00000 },\n    { asset: 'ETH', free: 0.0876, locked: 0.0000 },\n    { asset: 'BNB', free: 0.654, locked: 0.000 },\n    { asset: 'USDT', free: 125.50, locked: 0.00 }\n  ]\n};\n\nconst response = `💳 *Cüzdan Bakiyesi*\n\n💰 *TRY Bakiye:*\n• Toplam: ${balance.totalTRY.toLocaleString('tr-TR')} TRY\n• Kullanılabilir: ${balance.availableTRY.toLocaleString('tr-TR')} TRY\n• Emirlerde: ${balance.inOrdersTRY.toLocaleString('tr-TR')} TRY\n\n🪙 *Kripto Bakiyeler:*\n${balance.cryptoBalances.filter(b => b.free > 0 || b.locked > 0).map(balance => \n  `• *${balance.asset}:* ${balance.free} ${balance.locked > 0 ? `(${balance.locked} kilitli)` : ''}`\n).join('\\n')}\n\n_Son güncelleme: ${new Date().toLocaleString('tr-TR')}_`;\n\nreturn [{\n  json: {\n    chatId: command.chatId,\n    response,\n    parseMode: 'Markdown'\n  }\n}];"
      },
      "id": "balance-handler",
      "name": "Balance Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 200]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Trading Signals Handler\nconst command = $input.all()[0].json;\n\n// Mock signals data (in real implementation, fetch from database)\nconst signals = [\n  {\n    symbol: 'BTC',\n    type: 'BUY',\n    reason: 'RSI Oversold + MACD Bullish',\n    confidence: 85,\n    price: 1346789,\n    timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()\n  },\n  {\n    symbol: 'ETH',\n    type: 'HOLD',\n    reason: 'Sideways trend',\n    confidence: 60,\n    price: 82456,\n    timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString()\n  },\n  {\n    symbol: 'MARKET',\n    type: 'BUY',\n    reason: 'Fear & Greed Index: Extreme Fear',\n    confidence: 90,\n    price: null,\n    timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()\n  }\n];\n\nconst marketSentiment = {\n  sentiment: 'BULLISH',\n  confidence: 78,\n  fearGreedIndex: 25\n};\n\nfunction getSignalEmoji(type) {\n  switch(type) {\n    case 'BUY': return '🟢';\n    case 'SELL': return '🔴';\n    case 'HOLD': return '🟡';\n    default: return '⚪';\n  }\n}\n\nfunction getConfidenceEmoji(confidence) {\n  if (confidence >= 80) return '🔥';\n  if (confidence >= 70) return '⭐';\n  if (confidence >= 60) return '👍';\n  return '🤔';\n}\n\nconst response = `📊 *Trading Sinyalleri*\n\n🎯 *Genel Piyasa:* ${marketSentiment.sentiment} (${marketSentiment.confidence}%)\n😨 *Fear & Greed:* ${marketSentiment.fearGreedIndex}/100\n\n*Son Sinyaller:*\n${signals.map(signal => {\n  const timeAgo = Math.floor((Date.now() - new Date(signal.timestamp).getTime()) / (1000 * 60));\n  return `${getSignalEmoji(signal.type)} *${signal.symbol}* - ${signal.type} ${getConfidenceEmoji(signal.confidence)}\n   📝 ${signal.reason}\n   💪 Güven: ${signal.confidence}%\n   ⏰ ${timeAgo} dakika önce`;\n}).join('\\n\\n')}\n\n⚠️ *Uyarı:* Bu sinyaller yatırım tavsiyesi değildir. Kendi araştırmanızı yapın.\n\n_Son güncelleme: ${new Date().toLocaleString('tr-TR')}_`;\n\nreturn [{\n  json: {\n    chatId: command.chatId,\n    response,\n    parseMode: 'Markdown'\n  }\n}];"
      },
      "id": "signals-handler",
      "name": "Signals Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 300]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Help Handler\nconst command = $input.all()[0].json;\n\nconst response = `🤖 *Kripto Trading Bot Yardım*\n\n*📊 Bilgi Komutları:*\n/status - Portfolio durumu\n/balance - Cüzdan bakiyesi\n/signals - Trading sinyalleri\n/analyze [coin] - Coin analizi\n\n*💰 Trading Komutları:*\n/buy [coin] [miktar] - Alım emri\n/sell [coin] [miktar] - Satım emri\n/stop - Otomatik trading durdur\n/resume - Otomatik trading başlat\n\n*📈 Analiz Komutları:*\n/news - Son kripto haberleri\n/fear - Fear & Greed Index\n/market - Genel piyasa durumu\n\n*⚙️ Ayar Komutları:*\n/settings - Bot ayarları\n/risk - Risk parametreleri\n/alerts - Bildirim ayarları\n\n*Örnekler:*\n• \\`/buy BTC 100\\` - 100 TRY Bitcoin al\n• \\`/sell ETH 50%\\` - Ethereum'un %50'sini sat\n• \\`/analyze BTC\\` - Bitcoin analizi yap\n\n⚠️ *Önemli:* Bu bot deneysel amaçlıdır. Küçük tutarlarla test edin!\n\n📞 *Destek:* @inkbytefo`;\n\nreturn [{\n  json: {\n    chatId: command.chatId,\n    response,\n    parseMode: 'Markdown'\n  }\n}];"
      },
      "id": "help-handler",
      "name": "Help Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 400]
    },
    {
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Unknown Command Handler\nconst command = $input.all()[0].json;\n\nlet response;\n\nif (command.authorized === false) {\n  response = command.response;\n} else {\n  response = `❓ *Bilinmeyen Komut*\n\nAnlamadığım bir komut gönderdiniz: \\`${command.rawText}\\`\n\nYardım için /help komutunu kullanabilirsiniz.\n\n*Popüler komutlar:*\n• /status - Portfolio durumu\n• /signals - Trading sinyalleri\n• /balance - Cüzdan bakiyesi\n• /help - Tüm komutlar`;\n}\n\nreturn [{\n  json: {\n    chatId: command.chatId,\n    response,\n    parseMode: 'Markdown'\n  }\n}];"
      },
      "id": "unknown-handler",
      "name": "Unknown Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 500]
    },
    {
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "={{ $json.chatId }}",
        "text": "={{ $json.response }}",
        "additionalFields": {\n          \"parse_mode\": \"={{ $json.parseMode || 'Markdown' }}\"\n        }\n      },\n      \"id\": \"telegram-response\",\n      \"name\": \"Telegram Response\",\n      \"type\": \"n8n-nodes-base.telegram\",\n      \"typeVersion\": 1.2,\n      \"position\": [1120, 300]\n    }\n  ],\n  \"connections\": {\n    \"Telegram Trigger\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Command Parser\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Command Parser\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Command Switch\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Command Switch\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Status Handler\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"Balance Handler\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [],\n        [],\n        [\n          {\n            \"node\": \"Signals Handler\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [],\n        [\n          {\n            \"node\": \"Help Handler\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"Help Handler\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ],\n        [\n          {\n            \"node\": \"Unknown Handler\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Status Handler\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Telegram Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Balance Handler\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Telegram Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Signals Handler\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Telegram Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Help Handler\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Telegram Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    },\n    \"Unknown Handler\": {\n      \"main\": [\n        [\n          {\n            \"node\": \"Telegram Response\",\n            \"type\": \"main\",\n            \"index\": 0\n          }\n        ]\n      ]\n    }\n  },\n  \"settings\": {\n    \"executionOrder\": \"v1\"\n  },\n  \"staticData\": null,\n  \"tags\": [\"telegram\", \"bot\", \"trading\"],\n  \"triggerCount\": 1,\n  \"updatedAt\": \"2024-01-20T10:00:00.000Z\",\n  \"versionId\": \"1\"\n}
