# Kripto Trading Sistemi - n8n Uygulama Planı

## 🎯 Sistem Genel Bakış

Bu sistem, Telegram AI chatbot ile yö<PERSON>n, ger<PERSON><PERSON> zamanlı piyasa analizi yapan ve Binance TR entegrasyonlu bir kripto trading sistemidir.

## 📊 Sistem Mimarisi

### Temel Bileşenler:
1. **Veri Toplama Katmanı**: Market data, haberler, sentiment
2. **AI Analiz Motoru**: OpenAI GPT-4 ile akıllı analiz
3. **Trading Execution**: Binance TR API entegrasyonu
4. **User Interface**: Telegram Bot kontrolü
5. **Risk Management**: Stop-loss, position sizing

## 🔧 n8n Workflow'ları

### Workflow 1: Market Data Collector
```json
{
  "name": "Market Data Collector",
  "nodes": [
    {
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "parameters": {
        "rule": {
          "interval": [{"field": "minutes", "value": 1}]
        }
      }
    },
    {
      "name": "CoinGecko - BTC Price",
      "type": "n8n-nodes-base.coinGecko",
      "parameters": {
        "resource": "coin",
        "operation": "price",
        "coinId": "bitcoin",
        "quoteCurrencies": ["usd", "try"]
      }
    },
    {
      "name": "Binance TR API",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "https://api.binance.com/api/v3/ticker/24hr",
        "method": "GET",
        "qs": {
          "symbol": "BTCTRY"
        }
      }
    },
    {
      "name": "Technical Analysis",
      "type": "n8n-nodes-base.code",
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// RSI, MACD, Bollinger Bands hesaplamaları"
      }
    }
  ]
}
```

### Workflow 2: AI Market Analyzer
```json
{
  "name": "AI Market Analyzer",
  "nodes": [
    {
      "name": "Data Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "parameters": {
        "rule": {
          "interval": [{"field": "minutes", "value": 2}]
        }
      }
    },
    {
      "name": "OpenAI Analysis",
      "type": "@n8n/n8n-nodes-langchain.openAi",
      "parameters": {
        "resource": "text",
        "operation": "message",
        "modelId": "gpt-4",
        "prompt": "Kripto piyasa verilerini analiz et ve trading sinyali üret"
      }
    },
    {
      "name": "Signal Generator",
      "type": "n8n-nodes-base.code",
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Trading sinyali üretme algoritması"
      }
    }
  ]
}
```

### Workflow 3: Telegram Bot Handler
```json
{
  "name": "Telegram Bot Handler",
  "nodes": [
    {
      "name": "Telegram Trigger",
      "type": "n8n-nodes-base.telegramTrigger",
      "parameters": {
        "updates": ["message"]
      }
    },
    {
      "name": "Command Parser",
      "type": "n8n-nodes-base.switch",
      "parameters": {
        "rules": [
          {"condition": "contains", "value": "/status"},
          {"condition": "contains", "value": "/balance"},
          {"condition": "contains", "value": "/buy"},
          {"condition": "contains", "value": "/sell"}
        ]
      }
    },
    {
      "name": "Telegram Response",
      "type": "n8n-nodes-base.telegram",
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "={{$node['Telegram Trigger'].json['message']['chat']['id']}}",
        "text": "İşlem tamamlandı"
      }
    }
  ]
}
```

## 🔐 Güvenlik ve Risk Yönetimi

### API Güvenliği:
- Binance TR API anahtarları environment variables'da
- IP whitelist kullanımı
- Read-only ve trading permissions ayrımı

### Risk Kontrolü:
- Maksimum pozisyon büyüklüğü: %5 portfolio
- Stop-loss: %2-3 zarar durumunda otomatik satış
- Take-profit: %5-8 kar durumunda otomatik satış
- Daily loss limit: %10 günlük zarar limiti

### Position Sizing:
```javascript
// Risk bazlı position sizing
const accountBalance = 1000; // TRY
const riskPerTrade = 0.02; // %2 risk
const stopLossDistance = 0.03; // %3 stop-loss
const positionSize = (accountBalance * riskPerTrade) / stopLossDistance;
```

## 📱 Telegram Bot Komutları

### Temel Komutlar:
- `/start` - Bot'u başlat
- `/status` - Portfolio durumu
- `/balance` - Cüzdan bakiyesi
- `/signals` - Son trading sinyalleri
- `/performance` - Performans raporu

### Trading Komutları:
- `/buy BTC 100` - 100 TRY Bitcoin al
- `/sell BTC 50%` - Bitcoin'in %50'sini sat
- `/stop` - Otomatik trading'i durdur
- `/resume` - Otomatik trading'i devam ettir

### Analiz Komutları:
- `/analyze BTC` - Bitcoin analizi
- `/news` - Son kripto haberleri
- `/fear` - Fear & Greed Index

## 🚀 Kurulum Adımları

### 1. n8n Kurulumu:
```bash
# Docker ile n8n kurulumu
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -v ~/.n8n:/home/<USER>/.n8n \
  n8nio/n8n
```

### 2. Environment Variables:
```env
# Binance TR API
BINANCE_API_KEY=your_api_key
BINANCE_SECRET_KEY=your_secret_key

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# OpenAI
OPENAI_API_KEY=your_openai_key

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/crypto_trading
```

### 3. Database Schema:
```sql
-- Market data tablosu
CREATE TABLE market_data (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(10) NOT NULL,
  price DECIMAL(18,8) NOT NULL,
  volume DECIMAL(18,8),
  timestamp TIMESTAMP DEFAULT NOW()
);

-- Trading signals tablosu
CREATE TABLE trading_signals (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(10) NOT NULL,
  signal_type VARCHAR(10) NOT NULL, -- BUY, SELL, HOLD
  confidence DECIMAL(5,2) NOT NULL,
  price DECIMAL(18,8) NOT NULL,
  timestamp TIMESTAMP DEFAULT NOW()
);

-- Trades tablosu
CREATE TABLE trades (
  id SERIAL PRIMARY KEY,
  symbol VARCHAR(10) NOT NULL,
  side VARCHAR(10) NOT NULL, -- BUY, SELL
  quantity DECIMAL(18,8) NOT NULL,
  price DECIMAL(18,8) NOT NULL,
  status VARCHAR(20) DEFAULT 'PENDING',
  timestamp TIMESTAMP DEFAULT NOW()
);
```

## 📈 Performans Metrikleri

### Takip Edilecek KPI'lar:
- **Toplam Return**: %
- **Sharpe Ratio**: Risk-adjusted return
- **Max Drawdown**: Maksimum kayıp
- **Win Rate**: Kazanan işlem oranı
- **Profit Factor**: Kar/Zarar oranı

### Raporlama:
- Günlük performans raporu (Telegram)
- Haftalık detaylı analiz
- Aylık portfolio review

## 🔄 Sürekli İyileştirme

### A/B Testing:
- Farklı AI prompt'ları test etme
- Risk parametrelerini optimize etme
- Signal threshold'larını ayarlama

### Machine Learning:
- Historical data ile model training
- Pattern recognition iyileştirme
- Sentiment analysis accuracy artırma

Bu sistem, güvenli ve karlı kripto trading için kapsamlı bir çözüm sunar. Küçük tutarlarla başlayıp, performansa göre yatırım miktarını artırabilirsiniz.
